#!/usr/bin/env python3
"""
WordPress to Supabase Migration Script
Migrates WordPress data from SQL dump to Supabase database
"""

import re
import json
import uuid
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import html

# Supabase Configuration
SUPABASE_URL = "https://cgmlpbxwmqynmshecaqn.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnbWxwYnh3bXF5bm1zaGVjYXFuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMjM0NzU5MiwiZXhwIjoyMDM3OTIzNTkyfQ.Ej7Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"  # Replace with your actual service key

# Headers for Supabase API
HEADERS = {
    "apikey": SUPABASE_SERVICE_KEY,
    "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
    "Content-Type": "application/json"
}

class WordPressToSupabaseMigrator:
    def __init__(self, sql_file_path: str):
        self.sql_file_path = sql_file_path
        self.users_map = {}
        self.categories_map = {}
        self.posts_map = {}

    def read_sql_file(self) -> str:
        """Read the SQL file content"""
        with open(self.sql_file_path, 'r', encoding='utf-8') as file:
            return file.read()

    def extract_insert_data(self, content: str, table_name: str) -> List[Tuple]:
        """Extract INSERT data for a specific table"""
        pattern = rf"INSERT INTO `{table_name}` VALUES\s*\n(.*?);"
        matches = re.findall(pattern, content, re.DOTALL | re.MULTILINE)

        all_data = []
        for match in matches:
            # Parse the VALUES part
            values_text = match.strip()
            if values_text:
                # Split by ),( to get individual rows
                rows = self.parse_values(values_text)
                all_data.extend(rows)

        return all_data

    def parse_values(self, values_text: str) -> List[Tuple]:
        """Parse VALUES clause into individual rows"""
        rows = []
        # This is a simplified parser - in production, you'd want a more robust SQL parser
        # For now, we'll handle the basic cases

        # Remove outer parentheses and split by '),('
        values_text = values_text.strip()
        if values_text.startswith('(') and values_text.endswith(')'):
            values_text = values_text[1:-1]

        # Split by '),(' pattern
        row_strings = re.split(r'\),\s*\(', values_text)

        for row_string in row_strings:
            row_string = row_string.strip()
            if row_string:
                # Parse individual values - this is simplified
                values = self.parse_row_values(row_string)
                rows.append(tuple(values))

        return rows

    def parse_row_values(self, row_string: str) -> List:
        """Parse individual row values"""
        values = []
        current_value = ""
        in_quotes = False
        quote_char = None
        i = 0

        while i < len(row_string):
            char = row_string[i]

            if not in_quotes:
                if char in ["'", '"']:
                    in_quotes = True
                    quote_char = char
                    current_value += char
                elif char == ',':
                    values.append(self.clean_value(current_value.strip()))
                    current_value = ""
                else:
                    current_value += char
            else:
                if char == quote_char:
                    # Check if it's escaped
                    if i + 1 < len(row_string) and row_string[i + 1] == quote_char:
                        current_value += char + char
                        i += 1
                    else:
                        in_quotes = False
                        quote_char = None
                        current_value += char
                else:
                    current_value += char

            i += 1

        # Add the last value
        if current_value.strip():
            values.append(self.clean_value(current_value.strip()))

        return values

    def clean_value(self, value: str):
        """Clean and convert SQL values to Python values"""
        value = value.strip()

        if value == 'NULL':
            return None
        elif value.startswith("'") and value.endswith("'"):
            # String value
            return value[1:-1].replace("''", "'").replace("\\'", "'")
        elif value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
            # Integer
            return int(value)
        elif value.lower() in ['true', 'false']:
            return value.lower() == 'true'
        else:
            # Try to parse as number, otherwise return as string
            try:
                if '.' in value:
                    return float(value)
                else:
                    return int(value)
            except ValueError:
                return value

    def create_auth_user(self, email: str, user_id: str = None) -> str:
        """Create a user in Supabase Auth"""
        if not user_id:
            user_id = str(uuid.uuid4())

        # Create auth user
        auth_data = {
            "id": user_id,
            "email": email,
            "encrypted_password": "$2a$10$dummy.hash.for.testing.purposes",
            "email_confirmed_at": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "raw_app_meta_data": {"provider": "email", "providers": ["email"]},
            "raw_user_meta_data": {},
            "is_super_admin": False,
            "role": "authenticated"
        }

        # Insert into auth.users via SQL
        query = """
        INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, role)
        VALUES (%(id)s, %(email)s, %(encrypted_password)s, %(email_confirmed_at)s, %(created_at)s, %(updated_at)s, %(raw_app_meta_data)s, %(raw_user_meta_data)s, %(is_super_admin)s, %(role)s)
        ON CONFLICT (id) DO NOTHING
        RETURNING id;
        """

        response = self.execute_sql(query, auth_data)
        return user_id

    def execute_sql(self, query: str, params: Dict = None) -> Dict:
        """Execute SQL query via Supabase API"""
        url = f"{SUPABASE_URL}/rest/v1/rpc/execute_sql"

        payload = {"query": query}
        if params:
            # For parameterized queries, we'll format them directly for simplicity
            for key, value in params.items():
                if isinstance(value, str):
                    query = query.replace(f"%({key})s", f"'{value}'")
                elif isinstance(value, dict):
                    query = query.replace(f"%({key})s", f"'{json.dumps(value)}'")
                elif value is None:
                    query = query.replace(f"%({key})s", "NULL")
                else:
                    query = query.replace(f"%({key})s", str(value))
            payload["query"] = query

        response = requests.post(url, headers=HEADERS, json=payload)

        if response.status_code != 200:
            print(f"SQL Error: {response.text}")
            return {}

        return response.json()

    def migrate_users(self, content: str):
        """Migrate WordPress users to Supabase"""
        print("Migrating users...")

        # Extract WordPress users
        wp_users = self.extract_insert_data(content, 'wp_users')

        for user_data in wp_users:
            if len(user_data) >= 10:  # Ensure we have enough fields
                wp_id, login, password, nicename, email, url, registered, activation_key, status, display_name = user_data[:10]

                # Create auth user
                user_uuid = str(uuid.uuid4())
                self.create_auth_user(email, user_uuid)

                # Determine role
                role = 'admin' if login == 'Dhananjay' else 'user'

                # Create user profile
                user_profile = {
                    "id": user_uuid,
                    "email": email,
                    "full_name": display_name,
                    "username": login,
                    "role": role,
                    "created_at": registered,
                    "updated_at": datetime.now().isoformat()
                }

                # Insert user profile
                self.insert_record('users', user_profile)
                self.users_map[wp_id] = user_uuid

                print(f"Migrated user: {login} ({email})")

    def migrate_categories(self, content: str):
        """Migrate WordPress categories to Supabase"""
        print("Migrating categories...")

        # Extract terms and term_taxonomy
        wp_terms = self.extract_insert_data(content, 'wp_terms')
        wp_term_taxonomy = self.extract_insert_data(content, 'wp_term_taxonomy')

        # Create a map of term_id to taxonomy info
        taxonomy_map = {}
        for tax_data in wp_term_taxonomy:
            if len(tax_data) >= 6:
                term_taxonomy_id, term_id, taxonomy, description, parent, count = tax_data[:6]
                if taxonomy == 'category':  # Only migrate categories
                    taxonomy_map[term_id] = {
                        'description': description,
                        'parent': parent,
                        'count': count
                    }

        # Migrate categories
        for term_data in wp_terms:
            if len(term_data) >= 4:
                term_id, name, slug, term_group = term_data[:4]

                if term_id in taxonomy_map and name != 'Uncategorized':
                    category_uuid = str(uuid.uuid4())

                    category = {
                        "id": category_uuid,
                        "name": name,
                        "slug": slug,
                        "description": taxonomy_map[term_id]['description'] or f"{name} category",
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }

                    self.insert_record('categories', category)
                    self.categories_map[term_id] = category_uuid

                    print(f"Migrated category: {name}")

    def migrate_posts(self, content: str):
        """Migrate WordPress posts to Supabase"""
        print("Migrating posts...")

        # Extract posts
        wp_posts = self.extract_insert_data(content, 'wp_posts')

        # Get default author (first user)
        default_author = list(self.users_map.values())[0] if self.users_map else str(uuid.uuid4())

        # Get default category (first category)
        default_category = list(self.categories_map.values())[0] if self.categories_map else None

        for post_data in wp_posts:
            if len(post_data) >= 23:  # Ensure we have enough fields
                (post_id, post_author, post_date, post_date_gmt, post_content,
                 post_title, post_excerpt, post_status, comment_status, ping_status,
                 post_password, post_name, to_ping, pinged, post_modified,
                 post_modified_gmt, post_content_filtered, post_parent, guid,
                 menu_order, post_type, post_mime_type, comment_count) = post_data[:23]

                # Only migrate published posts (not pages, attachments, etc.)
                if post_type == 'post' and post_status == 'publish' and post_title:
                    post_uuid = str(uuid.uuid4())

                    # Clean content
                    clean_content = self.clean_wordpress_content(post_content)
                    clean_excerpt = self.clean_wordpress_content(post_excerpt) if post_excerpt else ""

                    # Get author
                    author_id = self.users_map.get(post_author, default_author)

                    # Extract tags from content or title
                    tags = self.extract_tags(post_title, clean_content)

                    post = {
                        "id": post_uuid,
                        "title": post_title,
                        "slug": post_name or self.slugify(post_title),
                        "excerpt": clean_excerpt[:500] if clean_excerpt else "",  # Limit excerpt length
                        "content": clean_content,
                        "status": "published",
                        "author_id": author_id,
                        "category_id": default_category,
                        "tags": tags,
                        "meta_title": post_title,
                        "meta_description": clean_excerpt[:160] if clean_excerpt else post_title[:160],
                        "published_at": post_date,
                        "created_at": post_date,
                        "updated_at": post_modified
                    }

                    self.insert_record('posts', post)
                    self.posts_map[post_id] = post_uuid

                    print(f"Migrated post: {post_title}")

    def migrate_comments(self, content: str):
        """Migrate WordPress comments to Supabase"""
        print("Migrating comments...")

        # Extract comments
        wp_comments = self.extract_insert_data(content, 'wp_comments')

        for comment_data in wp_comments:
            if len(comment_data) >= 15:
                (comment_id, comment_post_id, comment_author, comment_author_email,
                 comment_author_url, comment_author_ip, comment_date, comment_date_gmt,
                 comment_content, comment_karma, comment_approved, comment_agent,
                 comment_type, comment_parent, user_id) = comment_data[:15]

                # Only migrate approved comments
                if comment_approved == '1' and comment_post_id in self.posts_map:
                    comment_uuid = str(uuid.uuid4())

                    comment = {
                        "id": comment_uuid,
                        "post_id": self.posts_map[comment_post_id],
                        "author_id": self.users_map.get(user_id) if user_id and user_id != 0 else None,
                        "parent_id": None,  # We'll handle nested comments separately
                        "content": comment_content,
                        "author_name": comment_author,
                        "author_email": comment_author_email,
                        "status": "approved",
                        "created_at": comment_date,
                        "updated_at": comment_date
                    }

                    self.insert_record('comments', comment)
                    print(f"Migrated comment from: {comment_author}")

    def clean_wordpress_content(self, content: str) -> str:
        """Clean WordPress content from Gutenberg blocks and HTML"""
        if not content:
            return ""

        # Remove Gutenberg block comments
        content = re.sub(r'<!-- wp:.*? -->', '', content)
        content = re.sub(r'<!-- /wp:.*? -->', '', content)

        # Convert some basic HTML to markdown-like format
        content = re.sub(r'<h([1-6]).*?>(.*?)</h[1-6]>', r'\n## \2\n', content)
        content = re.sub(r'<p.*?>(.*?)</p>', r'\1\n\n', content)
        content = re.sub(r'<strong.*?>(.*?)</strong>', r'**\1**', content)
        content = re.sub(r'<em.*?>(.*?)</em>', r'*\1*', content)
        content = re.sub(r'<a.*?href="(.*?)".*?>(.*?)</a>', r'[\2](\1)', content)

        # Remove remaining HTML tags
        content = re.sub(r'<[^>]+>', '', content)

        # Clean up whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = content.strip()

        return content

    def extract_tags(self, title: str, content: str) -> List[str]:
        """Extract tags from title and content"""
        tags = []

        # Common keywords to extract as tags
        keywords = ['christmas', 'wishes', 'shayari', 'quotes', 'hindi', 'english',
                   'love', 'friendship', 'life', 'motivational', 'sad', 'happy',
                   'birthday', 'new year', 'festival', 'poetry', 'status']

        text = (title + " " + content).lower()

        for keyword in keywords:
            if keyword in text:
                tags.append(keyword)

        return tags[:5]  # Limit to 5 tags

    def slugify(self, text: str) -> str:
        """Convert text to URL-friendly slug"""
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        # Convert to lowercase
        text = text.lower()
        # Replace spaces and special chars with hyphens
        text = re.sub(r'[^\w\s-]', '', text)
        text = re.sub(r'[-\s]+', '-', text)
        # Remove leading/trailing hyphens
        text = text.strip('-')
        return text[:100]  # Limit length

    def insert_record(self, table: str, data: Dict):
        """Insert a record into Supabase table"""
        url = f"{SUPABASE_URL}/rest/v1/{table}"

        response = requests.post(url, headers=HEADERS, json=data)

        if response.status_code not in [200, 201]:
            print(f"Error inserting into {table}: {response.text}")
            print(f"Data: {data}")

        return response.status_code in [200, 201]

    def run_migration(self):
        """Run the complete migration process"""
        print("Starting WordPress to Supabase migration...")

        # Read SQL file
        print("Reading SQL file...")
        content = self.read_sql_file()

        # Run migrations in order
        self.migrate_users(content)
        self.migrate_categories(content)
        self.migrate_posts(content)
        self.migrate_comments(content)

        print("Migration completed!")
        print(f"Migrated {len(self.users_map)} users")
        print(f"Migrated {len(self.categories_map)} categories")
        print(f"Migrated {len(self.posts_map)} posts")


def main():
    """Main function to run the migration"""
    sql_file_path = "u957990218_GpBKT.zayotech-com.20250727190356.sql"

    # Check if file exists
    import os
    if not os.path.exists(sql_file_path):
        print(f"Error: SQL file '{sql_file_path}' not found!")
        print("Please make sure the SQL file is in the same directory as this script.")
        return

    # Create migrator and run
    migrator = WordPressToSupabaseMigrator(sql_file_path)
    migrator.run_migration()


if __name__ == "__main__":
    main()