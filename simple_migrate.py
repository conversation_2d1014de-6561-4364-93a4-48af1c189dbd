#!/usr/bin/env python3
"""
Simple WordPress to Supabase Migration Script
"""

import re
import json
import uuid
import requests
from datetime import datetime

# You'll need to replace this with your actual Supabase service key
SUPABASE_PROJECT_ID = "cgmlpbxwmqynmshecaqn"
SUPABASE_API_URL = f"https://{SUPABASE_PROJECT_ID}.supabase.co"

# This is a placeholder - you need to get your actual service key from Supabase dashboard
SUPABASE_SERVICE_KEY = "YOUR_SERVICE_KEY_HERE"

def execute_supabase_query(query, params=None):
    """Execute SQL query via Supabase Management API"""
    url = f"https://api.supabase.com/v1/projects/{SUPABASE_PROJECT_ID}/database/query"
    
    headers = {
        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {"query": query}
    
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code != 200:
        print(f"Error: {response.status_code} - {response.text}")
        return None
    
    return response.json()

def create_sample_data():
    """Create sample data based on WordPress content"""
    
    print("Creating sample users...")
    
    # Create auth user first
    auth_user_query = """
    INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, role)
    VALUES ('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', '$2a$10$dummy.hash.for.testing.purposes', NOW(), NOW(), NOW(), '{"provider": "email", "providers": ["email"]}', '{}', false, 'authenticated')
    ON CONFLICT (id) DO NOTHING;
    """
    
    execute_supabase_query(auth_user_query)
    
    # Create user profile
    user_query = """
    INSERT INTO users (id, email, full_name, username, role, created_at, updated_at)
    VALUES ('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'Dhananjay', 'dhananjay', 'admin', '2023-07-20 22:58:21', NOW())
    ON CONFLICT (id) DO UPDATE SET
        full_name = EXCLUDED.full_name,
        username = EXCLUDED.username,
        role = EXCLUDED.role,
        updated_at = EXCLUDED.updated_at;
    """
    
    execute_supabase_query(user_query)
    
    print("Creating categories...")
    
    # Create categories
    categories_query = """
    INSERT INTO categories (id, name, slug, description, created_at, updated_at) VALUES
    ('d9cb2030-9068-49dd-b306-8f2f3d686003', 'English', 'english', 'English quotes, wishes and content for social media and daily inspiration', NOW(), NOW()),
    ('6aaac1e6-d3fb-46f1-8e48-8510d439a147', 'Hindi', 'hindi', 'Hindi content including shayari, quotes and wishes', NOW(), NOW()),
    ('8668a078-837b-4b22-9b5a-beff5bb53dfa', 'Shayari', 'shayari', 'Beautiful Hindi Shayari collection for love, friendship, and life', NOW(), NOW()),
    ('81767b3c-b538-4dfb-83b9-68ef1278a1d6', 'Quotes', 'quotes', 'Inspirational and motivational quotes', NOW(), NOW()),
    ('102e6d17-ed66-4019-bdfc-1f147fb1859f', 'Status', 'status', 'Status updates and captions for social media', NOW(), NOW()),
    ('f3b7e4f5-d01f-4f9a-bd3a-6e12f55259d7', 'Jokes', 'jokes', 'Funny jokes and humor content', NOW(), NOW()),
    ('0ab6a315-5537-47ee-a280-56bc2aca9472', 'Poetries', 'poetries', 'Beautiful poetry and verses', NOW(), NOW()),
    ('a346cbbb-2f51-4cfe-b5fe-eac79836b775', 'Stories', 'stories', 'Short stories and narratives', NOW(), NOW())
    ON CONFLICT (id) DO NOTHING;
    """
    
    execute_supabase_query(categories_query)
    
    print("Creating posts...")
    
    # Create sample posts
    posts_query = """
    INSERT INTO posts (id, title, slug, excerpt, content, featured_image, status, author_id, category_id, tags, meta_title, meta_description, published_at, created_at, updated_at) VALUES
    (
        '74a8a6ff-c071-4b5f-b9ba-e610e1e95fc7',
        '121+ BEST Merry Christmas Wishes images and Messages',
        'merry-christmas-wishes',
        'This Christmas, don''t miss the chance to make your loved ones happy. With these lovely Merry Christmas Wishes, you can show your love to your friends, lover, family, or other relatives.',
        'Merry Christmas Wishes: Friends and family get together for Christmas. We often take the love in our lives for granted, but this helps us value it more. This Christmas, don''t miss the chance to make your loved ones happy. With these lovely Merry Christmas Wishes, you can show your love to your friends, lover, family, or other relatives.

Christmas is a time to get back in touch with people, and your message is a great way to do that. A simple Christmas letter can be the thing that brings you back together.

## Merry Christmas Wishes Images

**Wishing you love, joy, and peace this Christmas.**

**May this season find you among those you love, sharing in the twin glories of generosity and gratitude.**

**May the Christmas Season bring only happiness and joy to you and your family.**

**Peace and love to you at this holiday season and in all the seasons of the year to come. Merry Christmas.**

**Merry Christmas and Happy New Year from our family to yours.**

**Wishing you Happy Holidays and a New Year that makes you smile every day**

**Christmas is the day that holds all time together.**

**Wishing your holiday season be filled with sparkles of joy and love. Merry Christmas to you and your family!**',
        'https://zayotech.com/wp-content/uploads/2023/12/Merry-Christmas-Wishes.jpg',
        'published',
        '550e8400-e29b-41d4-a716-446655440000',
        'd9cb2030-9068-49dd-b306-8f2f3d686003',
        ARRAY['christmas', 'wishes', 'greetings', 'holiday', 'family'],
        'Merry Christmas Wishes - 121+ Best Images and Messages',
        '121+ BEST Merry Christmas Wishes images and Messages for friends and family. Express your love with beautiful Christmas greetings.',
        '2023-12-05 20:18:57',
        '2023-12-05 20:18:57',
        '2025-06-18 21:37:14'
    ),
    (
        'b8f3c2d1-4e5f-6789-abcd-ef0123456789',
        'Best Hindi Shayari Collection for Love and Life',
        'best-hindi-shayari-collection',
        'Discover the most beautiful collection of Hindi Shayari for love, friendship, and life. Express your emotions with these heartfelt verses.',
        'Hindi Shayari is a beautiful form of poetry that expresses deep emotions and feelings. Whether you want to express love, friendship, sadness, or joy, Shayari provides the perfect words to convey your heart''s message.

## Love Shayari

**दिल की बात कहने का यह अंदाज़ है शायरी**
**प्रेम की भाषा में लिखा गया यह राज़ है शायरी**

**तेरे बिना अधूरी है मेरी हर खुशी**
**तू ही तो मेरी जिंदगी की पूरी खुशी**

## Friendship Shayari

**दोस्ती का रिश्ता है बहुत प्यारा**
**सच्चे दोस्त मिलना है नसीब वालों का**

**दोस्त वो होता है जो हर गम में साथ दे**
**खुशियों में शामिल हो और दुःख में हाथ दे**

## Life Shayari

**जिंदगी एक सफर है सुहाना**
**यहाँ कल क्या हो किसने जाना**

**उम्मीदों का दामन थामे रखना**
**जिंदगी में हमेशा मुस्कराते रहना**',
        'https://example.com/hindi-shayari.jpg',
        'published',
        '550e8400-e29b-41d4-a716-446655440000',
        '8668a078-837b-4b22-9b5a-beff5bb53dfa',
        ARRAY['hindi', 'shayari', 'poetry', 'love', 'friendship', 'life'],
        'Best Hindi Shayari Collection for Love and Life',
        'Beautiful Hindi Shayari collection for love, friendship, and life. Express your emotions with heartfelt verses and poetry.',
        '2023-08-15 10:30:00',
        '2023-08-15 10:30:00',
        '2023-08-15 10:30:00'
    ),
    (
        'c9e4d3f2-5a6b-7890-bcde-f12345678901',
        'Inspirational Quotes for Daily Motivation',
        'inspirational-quotes-daily-motivation',
        'Start your day with these powerful inspirational quotes that will motivate you to achieve your goals and live your best life.',
        'Motivation is the fuel that drives us towards our goals. These inspirational quotes will help you stay focused, positive, and determined in your journey of life.

## Success Quotes

**"Success is not final, failure is not fatal: it is the courage to continue that counts."**

**"The only way to do great work is to love what you do."**

**"Don''t watch the clock; do what it does. Keep going."**

## Life Quotes

**"Life is what happens to you while you''re busy making other plans."**

**"The purpose of our lives is to be happy."**

**"In the end, we will remember not the words of our enemies, but the silence of our friends."**

## Motivational Quotes

**"Believe you can and you''re halfway there."**

**"It does not matter how slowly you go as long as you do not stop."**

**"Everything you''ve ever wanted is on the other side of fear."**',
        'https://example.com/inspirational-quotes.jpg',
        'published',
        '550e8400-e29b-41d4-a716-446655440000',
        '81767b3c-b538-4dfb-83b9-68ef1278a1d6',
        ARRAY['quotes', 'motivation', 'inspiration', 'success', 'life'],
        'Inspirational Quotes for Daily Motivation',
        'Powerful inspirational quotes for daily motivation. Start your day with positive thoughts and achieve your goals.',
        '2023-09-10 08:00:00',
        '2023-09-10 08:00:00',
        '2023-09-10 08:00:00'
    )
    ON CONFLICT (id) DO NOTHING;
    """
    
    execute_supabase_query(posts_query)
    
    print("Creating sample comments...")
    
    # Create sample comments
    comments_query = """
    INSERT INTO comments (id, post_id, author_id, parent_id, content, author_name, author_email, status, created_at, updated_at) VALUES
    (
        '11111111-2222-3333-4444-555555555555',
        '74a8a6ff-c071-4b5f-b9ba-e610e1e95fc7',
        NULL,
        NULL,
        'Beautiful collection of Christmas wishes! These will definitely help me express my feelings to my loved ones.',
        'Deepak',
        '<EMAIL>',
        'approved',
        '2023-12-06 10:30:00',
        '2023-12-06 10:30:00'
    ),
    (
        '22222222-3333-4444-5555-666666666666',
        'b8f3c2d1-4e5f-6789-abcd-ef0123456789',
        NULL,
        NULL,
        'बहुत खूबसूरत शायरी है। दिल को छू गई।',
        'Rahul',
        '<EMAIL>',
        'approved',
        '2023-08-16 14:20:00',
        '2023-08-16 14:20:00'
    ),
    (
        '33333333-4444-5555-6666-777777777777',
        'c9e4d3f2-5a6b-7890-bcde-f12345678901',
        NULL,
        NULL,
        'These quotes are exactly what I needed today. Thank you for sharing such motivational content!',
        'Priya',
        '<EMAIL>',
        'approved',
        '2023-09-11 09:15:00',
        '2023-09-11 09:15:00'
    )
    ON CONFLICT (id) DO NOTHING;
    """
    
    execute_supabase_query(comments_query)
    
    print("Sample data created successfully!")

def main():
    """Main function"""
    print("WordPress to Supabase Migration - Sample Data Creation")
    print("=" * 50)
    
    if SUPABASE_SERVICE_KEY == "YOUR_SERVICE_KEY_HERE":
        print("ERROR: Please update the SUPABASE_SERVICE_KEY in the script with your actual service key.")
        print("You can find this in your Supabase project settings under API keys.")
        return
    
    create_sample_data()
    
    print("\nMigration completed!")
    print("Your Supabase database now contains:")
    print("- 1 admin user (<EMAIL>)")
    print("- 8 categories (English, Hindi, Shayari, Quotes, etc.)")
    print("- 3 sample posts with content")
    print("- 3 sample comments")

if __name__ == "__main__":
    main()
